import { NextRequest, NextResponse } from 'next/server';
import { getDocumentByCode } from '@/lib/database';

/**
 * GET /api/documents/verify/[code] - Verify document by code
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ code: string }> }
) {
  try {
    const { code } = await params;
    
    if (!code) {
      return NextResponse.json(
        { error: 'Document code is required' },
        { status: 400 }
      );
    }

    // Get document by code
    const document = await getDocumentByCode(code);
    
    if (!document) {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }

    // Only return verification info for approved documents
    if (document.status !== 'approved') {
      return NextResponse.json(
        { error: 'Document is not approved' },
        { status: 403 }
      );
    }

    // Parse document data if it exists
    let documentData = {};
    if (document.document_data) {
      try {
        const dataString = document.document_data.toString();
        documentData = JSON.parse(dataString);
      } catch (err) {
        console.error('Error parsing document data:', err);
      }
    }

    // Return public verification information
    return NextResponse.json({
      verified: true,
      document: {
        id: document.id,
        document_name: document.document_name,
        applicant_name: document.applicant_name,
        code: document.code,
        status: document.status,
        approved_at: document.approved_at,
        uploaded_at: document.uploaded_at,
        document_data: documentData
      }
    });

  } catch (error) {
    console.error('Error verifying document:', error);
    return NextResponse.json(
      { error: 'Failed to verify document' },
      { status: 500 }
    );
  }
}
