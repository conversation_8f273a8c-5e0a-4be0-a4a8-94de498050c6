/**
 * QR Code utilities for document verification
 */

import QRCode from 'qrcode';

export interface QRCodeOptions {
  width?: number;
  margin?: number;
  color?: {
    dark?: string;
    light?: string;
  };
}

/**
 * Generate QR code for document verification
 */
export async function generateDocumentQRCode(
  documentCode: string,
  baseURL?: string,
  options: QRCodeOptions = {}
): Promise<string> {
  // Default options
  const defaultOptions: QRCodeOptions = {
    width: 150,
    margin: 1,
    color: {
      dark: '#000000',
      light: '#FFFFFF',
    },
  };

  const qrOptions = { ...defaultOptions, ...options };

  // Construct verification URL
  const verificationURL = `${baseURL || window.location.origin}/verify/${documentCode}`;

  try {
    // Generate QR code as data URL
    const qrCodeDataURL = await QRCode.toDataURL(verificationURL, qrOptions);
    return qrCodeDataURL;
  } catch (error) {
    console.error('Error generating QR code:', error);
    throw new Error('Failed to generate QR code');
  }
}

/**
 * Generate QR code for server-side use (returns base64 string)
 */
export async function generateDocumentQRCodeServer(
  documentCode: string,
  baseURL: string,
  options: QRCodeOptions = {}
): Promise<string> {
  // Default options for server-side generation
  const defaultOptions: QRCodeOptions = {
    width: 150,
    margin: 1,
    color: {
      dark: '#000000',
      light: '#FFFFFF',
    },
  };

  const qrOptions = { ...defaultOptions, ...options };

  // Construct verification URL
  const verificationURL = `${baseURL}/verify/${documentCode}`;

  try {
    // Generate QR code as data URL
    const qrCodeDataURL = await QRCode.toDataURL(verificationURL, qrOptions);
    return qrCodeDataURL;
  } catch (error) {
    console.error('Error generating QR code on server:', error);
    throw new Error('Failed to generate QR code');
  }
}

/**
 * Create QR code image element for canvas rendering
 */
export function createQRCodeImage(qrCodeDataURL: string): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error('Failed to load QR code image'));
    img.src = qrCodeDataURL;
  });
}

/**
 * Add QR code to canvas at specified position
 */
export function addQRCodeToCanvas(
  canvas: HTMLCanvasElement,
  qrCodeImage: HTMLImageElement,
  x: number = 20,
  y?: number,
  size: number = 100
): void {
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    throw new Error('Canvas context not available');
  }

  // Default to bottom-left position if y not specified
  const yPosition = y !== undefined ? y : canvas.height - size - 20;

  // Draw QR code on canvas
  ctx.drawImage(qrCodeImage, x, yPosition, size, size);
}
