"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  CheckCircle, 
  AlertCircle, 
  FileText, 
  User, 
  Calendar,
  Shield,
  QrCode
} from "lucide-react";

interface DocumentData {
  [key: string]: string;
}

interface VerifiedDocument {
  id: number;
  document_name: string;
  applicant_name: string;
  code: string;
  status: string;
  approved_at: string;
  uploaded_at: string;
  document_data: DocumentData;
}

interface VerificationResponse {
  verified: boolean;
  document: VerifiedDocument;
}

export default function DocumentVerificationPage() {
  const params = useParams();
  const code = params.code as string;
  
  const [document, setDocument] = useState<VerifiedDocument | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const verifyDocument = async () => {
      if (!code) {
        setError("No document code provided");
        setLoading(false);
        return;
      }

      try {
        const response = await fetch(`/api/documents/verify/${code}`);
        
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Failed to verify document");
        }

        const data: VerificationResponse = await response.json();
        setDocument(data.document);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to verify document";
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    verifyDocument();
  }, [code]);

  if (loading) {
    return (
      <div className="min-h-screen bg-background p-4">
        <div className="max-w-4xl mx-auto space-y-6">
          <div className="text-center space-y-2">
            <Skeleton className="h-8 w-64 mx-auto" />
            <Skeleton className="h-4 w-48 mx-auto" />
          </div>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-32" />
            </CardHeader>
            <CardContent className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-1/2" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background p-4">
        <div className="max-w-4xl mx-auto space-y-6">
          <div className="text-center space-y-2">
            <div className="flex items-center justify-center gap-2">
              <QrCode className="h-8 w-8 text-muted-foreground" />
              <h1 className="text-3xl font-bold">Document Verification</h1>
            </div>
            <p className="text-muted-foreground">Verify the authenticity of LDIS documents</p>
          </div>
          
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  if (!document) {
    return null;
  }

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center gap-2">
            <Shield className="h-8 w-8 text-green-600" />
            <h1 className="text-3xl font-bold">Document Verified</h1>
          </div>
          <p className="text-muted-foreground">This document has been verified as authentic</p>
        </div>

        {/* Verification Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Verification Status
            </CardTitle>
            <CardDescription>Document authenticity confirmed</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-green-100 text-green-800">
                <CheckCircle className="h-3 w-3 mr-1" />
                Verified & Approved
              </Badge>
              <span className="text-sm text-muted-foreground">
                Code: {document.code}
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Document Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Document Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Document Name</label>
                <p className="text-sm">{document.document_name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Applicant Name</label>
                <p className="text-sm flex items-center gap-1">
                  <User className="h-3 w-3" />
                  {document.applicant_name}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Approved Date</label>
                <p className="text-sm flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {new Date(document.approved_at).toLocaleDateString()}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Upload Date</label>
                <p className="text-sm flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {new Date(document.uploaded_at).toLocaleDateString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Document Data */}
        {Object.keys(document.document_data).length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>Document Details</CardTitle>
              <CardDescription>Information contained in this document</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(document.document_data).map(([key, value]) => (
                  <div key={key}>
                    <label className="text-sm font-medium text-muted-foreground capitalize">
                      {key.replace(/_/g, ' ')}
                    </label>
                    <p className="text-sm">{value}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Footer */}
        <div className="text-center text-sm text-muted-foreground">
          <p>This verification was performed by the Legal Document Issuance System (LDIS)</p>
          <p>Verification performed on {new Date().toLocaleDateString()}</p>
        </div>
      </div>
    </div>
  );
}
