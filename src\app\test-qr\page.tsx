"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { generateDocumentQRCode } from "@/lib/qr-utils";

export default function TestQRPage() {
  const [documentCode, setDocumentCode] = useState("DOC-20250101-0001");
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGenerateQR = async () => {
    if (!documentCode.trim()) {
      setError("Please enter a document code");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const qrCode = await generateDocumentQRCode(documentCode);
      setQrCodeUrl(qrCode);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to generate QR code");
    } finally {
      setLoading(false);
    }
  };

  const handleTestVerification = () => {
    if (documentCode.trim()) {
      window.open(`/verify/${documentCode}`, '_blank');
    }
  };

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-2xl mx-auto space-y-6">
        <div className="text-center">
          <h1 className="text-3xl font-bold">QR Code Test</h1>
          <p className="text-muted-foreground">Test QR code generation and verification</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Generate QR Code</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium">Document Code</label>
              <Input
                value={documentCode}
                onChange={(e) => setDocumentCode(e.target.value)}
                placeholder="Enter document code (e.g., DOC-20250101-0001)"
              />
            </div>

            <div className="flex gap-2">
              <Button onClick={handleGenerateQR} disabled={loading}>
                {loading ? "Generating..." : "Generate QR Code"}
              </Button>
              <Button variant="outline" onClick={handleTestVerification}>
                Test Verification Page
              </Button>
            </div>

            {error && (
              <div className="text-red-600 text-sm">{error}</div>
            )}

            {qrCodeUrl && (
              <div className="space-y-2">
                <label className="text-sm font-medium">Generated QR Code</label>
                <div className="border rounded-lg p-4 text-center">
                  <img src={qrCodeUrl} alt="QR Code" className="mx-auto" />
                  <p className="text-xs text-muted-foreground mt-2">
                    Scan to verify document: {documentCode}
                  </p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <p>1. Enter a document code in the format: DOC-YYYYMMDD-NNNN</p>
            <p>2. Click "Generate QR Code" to create a verification QR code</p>
            <p>3. Click "Test Verification Page" to see what the QR code would show</p>
            <p>4. The QR code will link to: /verify/{documentCode}</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
